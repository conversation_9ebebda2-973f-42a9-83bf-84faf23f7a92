#!/usr/bin/env python3
"""
基准测试函数和分区机制分析
分析benchmark.py和partitions.py的功能和使用方法
"""

import sys
import os
import numpy as np

# 添加路径
sys.path.append('pyIAF')
sys.path.append('pyIAF/Tasks')
sys.path.append('pyIAF/utils')

def analyze_benchmark_functions():
    """分析基准测试函数"""
    print("=" * 60)
    print("基准测试函数分析 (benchmark.py)")
    print("=" * 60)
    
    try:
        from benchmark import create_tasks_diff_func
        
        # 创建18个不同的任务
        dimension = 10
        tasks = create_tasks_diff_func(dim=dimension, normalized=True)
        
        print(f"总共创建了 {len(tasks)} 个任务")
        print(f"维度: {dimension}")
        print(f"归一化: True")
        print()
        
        # 分析每个任务的特性
        task_info = [
            ("Task 1", "<PERSON><PERSON>wank", "CI-H", "[-100, 100]", "有旋转+平移"),
            ("Task 2", "Rastrigin", "CI-H", "[-50, 50]", "有旋转+平移"),
            ("Task 3", "Ackley", "CI-M", "[-50, 50]", "有旋转+平移"),
            ("Task 4", "Rastrigin", "CI-M", "[-50, 50]", "有旋转+平移"),
            ("Task 5", "Ackley", "CI-L", "[-50, 50]", "有旋转+平移"),
            ("Task 6", "Schwefel", "CI-L", "[-500, 500]", "无变换"),
            ("Task 7", "Rastrigin", "PI-H", "[-50, 50]", "有旋转+平移"),
            ("Task 8", "Sphere", "PI-H", "[-100, 100]", "仅平移"),
            ("Task 9", "Ackley", "PI-M", "[-50, 50]", "有旋转+平移"),
            ("Task 10", "Rosenbrock", "PI-M", "[-50, 50]", "无变换"),
            ("Task 11", "Ackley", "PI-L", "[-50, 50]", "有旋转+平移"),
            ("Task 12", "Weierstrass", "PI-L", "[-0.5, 0.5]", "有旋转+平移"),
            ("Task 13", "Rosenbrock", "NI-H", "[-50, 50]", "无变换"),
            ("Task 14", "Rastrigin", "NI-H", "[-50, 50]", "有旋转+平移"),
            ("Task 15", "Griewank", "NI-M", "[-100, 100]", "有旋转+平移"),
            ("Task 16", "Weierstrass", "NI-M", "[-0.5, 0.5]", "有旋转+平移"),
            ("Task 17", "Rastrigin", "NI-L", "[-50, 50]", "有旋转+平移"),
            ("Task 18", "Schwefel", "NI-L", "[-500, 500]", "无变换"),
        ]
        
        print("任务详细信息:")
        print(f"{'任务':<8} {'函数':<12} {'组别':<6} {'原始边界':<15} {'变换':<12}")
        print("-" * 60)
        for info in task_info:
            print(f"{info[0]:<8} {info[1]:<12} {info[2]:<6} {info[3]:<15} {info[4]:<12}")
        
        print()
        print("任务组别说明:")
        print("CI-H/M/L: 完全相关 (Complete Intersection) - 高/中/低相似度")
        print("PI-H/M/L: 部分相关 (Partial Intersection) - 高/中/低相似度") 
        print("NI-H/M/L: 无相关 (No Intersection) - 高/中/低相似度")
        
        # 测试几个任务的函数评估
        print("\n函数评估测试:")
        test_point = np.random.rand(dimension)  # 归一化输入 [0,1]
        
        for i, task in enumerate(tasks[:5]):  # 测试前5个任务
            try:
                result = task(test_point)
                print(f"Task {i+1} ({task}): f({test_point[:3]}...) = {result:.6f}")
            except Exception as e:
                print(f"Task {i+1} 评估失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"基准测试函数分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_partitions():
    """分析分区机制"""
    print("\n" + "=" * 60)
    print("分区机制分析 (partitions.py)")
    print("=" * 60)
    
    try:
        from partitions import create_normalized_partitions
        
        # 测试参数
        num_users = 6      # 客户端数量
        np_per_dim = 3     # 每维度分区数
        dim = 2            # 维度数（用于可视化）
        
        print(f"测试参数:")
        print(f"  客户端数量: {num_users}")
        print(f"  每维度分区数: {np_per_dim}")
        print(f"  维度: {dim}")
        print(f"  总分区数: {dim * np_per_dim} = {dim} × {np_per_dim}")
        print()
        
        # 创建分区
        partitions = create_normalized_partitions(num_users, np_per_dim, dim)
        
        print("Non-IID 分区结果:")
        print(f"{'客户端':<8} {'下界':<20} {'上界':<20} {'分区大小':<15}")
        print("-" * 70)
        
        for i, (lb, ub) in enumerate(partitions):
            lb_str = f"[{lb[0]:.3f}, {lb[1]:.3f}]"
            ub_str = f"[{ub[0]:.3f}, {ub[1]:.3f}]"
            size = np.prod(np.array(ub) - np.array(lb))
            print(f"客户端{i+1:<3} {lb_str:<20} {ub_str:<20} {size:.6f}")
        
        print()
        print("IID vs Non-IID 对比:")
        print("IID情况:")
        print("  - 所有客户端使用相同的搜索空间 [0, 1]^d")
        print("  - 使用LHS (Latin Hypercube Sampling) 采样")
        print("  - 数据分布相同，但样本不同")
        
        print("\nNon-IID情况:")
        print("  - 每个客户端分配不同的搜索空间子区域")
        print("  - 根据客户端ID和np_per_dim随机分配分区")
        print("  - 数据分布异构，模拟真实联邦学习场景")
        
        # 可视化分区（2D情况）
        if dim == 2:
            print(f"\n2D分区可视化 (每维度{np_per_dim}个分区):")
            grid = np.zeros((np_per_dim, np_per_dim), dtype=int)
            
            for i, (lb, ub) in enumerate(partitions):
                # 计算分区索引
                x_idx = int(lb[0] * np_per_dim)
                y_idx = int(lb[1] * np_per_dim)
                if x_idx < np_per_dim and y_idx < np_per_dim:
                    grid[y_idx, x_idx] = i + 1
            
            print("分区网格 (数字表示客户端ID):")
            for row in reversed(grid):  # 翻转以匹配坐标系
                print("  " + " ".join(f"{x:2d}" if x > 0 else " ." for x in row))
        
        return True
        
    except Exception as e:
        print(f"分区机制分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_usage():
    """演示如何在IAF-FBO中使用"""
    print("\n" + "=" * 60)
    print("在IAF-FBO中的使用示例")
    print("=" * 60)
    
    print("1. IID场景使用:")
    print("""
    # 所有客户端使用相同的搜索空间
    tasks = create_tasks_diff_func(dim=10, normalized=True)
    
    for client_id in range(18):
        task = tasks[client_id]
        # 使用LHS采样初始点
        initial_points = task.init_x_with_bound(size=50, iid=True)
        # 运行优化...
    """)
    
    print("2. Non-IID场景使用:")
    print("""
    # 为每个客户端分配不同的搜索空间
    num_clients = 18
    np_per_dim = 6  # 确保 dim * np_per_dim >= num_clients
    dim = 10
    
    partitions = create_normalized_partitions(num_clients, np_per_dim, dim)
    tasks = create_tasks_diff_func(dim=dim, normalized=True)
    
    for client_id in range(num_clients):
        task = tasks[client_id]
        pb = partitions[client_id]  # 该客户端的分区边界
        
        # 在分区内采样初始点
        initial_points = task.init_x_with_bound(size=50, iid=False, pb=pb)
        # 运行优化...
    """)
    
    print("3. 关键参数说明:")
    print("  - normalized=True: 使用归一化搜索空间 [0,1]^d")
    print("  - iid=True: 所有客户端共享相同搜索空间")
    print("  - iid=False: 客户端使用不同的搜索空间分区")
    print("  - np_per_dim: 控制分区粒度，值越大分区越细")
    
    print("\n4. 实际应用建议:")
    print("  - 对于算法对比实验，建议使用IID设置")
    print("  - 对于真实联邦学习场景，建议使用Non-IID设置")
    print("  - np_per_dim应满足: dim × np_per_dim ≥ num_clients")
    print("  - 可以通过调整np_per_dim控制数据异构程度")

def main():
    """主函数"""
    print("基准测试函数和分区机制完整分析")
    
    # 分析基准测试函数
    benchmark_success = analyze_benchmark_functions()
    
    # 分析分区机制  
    partition_success = analyze_partitions()
    
    # 演示使用方法
    demonstrate_usage()
    
    # 总结
    print("\n" + "=" * 60)
    print("分析总结")
    print("=" * 60)
    
    if benchmark_success and partition_success:
        print("✅ 所有分析成功完成!")
        print("\n核心要点:")
        print("1. benchmark.py 提供18个多任务优化问题，涵盖不同相似度级别")
        print("2. partitions.py 实现IID/Non-IID数据分布控制")
        print("3. 通过normalized参数统一搜索空间为[0,1]^d")
        print("4. 支持灵活的分区策略以模拟真实联邦学习场景")
    else:
        print("❌ 部分分析失败，请检查依赖项和文件路径")
    
    return benchmark_success and partition_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
