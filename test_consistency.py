#!/usr/bin/env python3
"""
Test script to verify IAF-FBO algorithm consistency
"""

import sys
import os
sys.path.append('pyIAF')

from core import IAF_FBO
import numpy as np

def test_basic_functionality():
    """Test basic functionality of IAF-FBO with MATLAB-matching parameters"""
    print("Testing IAF-FBO algorithm consistency...")

    # Test with MATLAB-matching configuration
    iaf_fbo = IAF_FBO(
        client_ids=[1, 2, 3],  # Test with 3 clients
        dimension=10,          # MATLAB D = 10
        n_initial=20,          # Smaller for testing
        max_fe=3,              # Fewer iterations for testing
        ucb_flag=2,            # LCB (MATLAB UCB_Flag = 2)
        n_clusters=2,          # Fewer clusters for testing
        pop_size=30,           # Smaller population for testing
        max_iter=10,           # Fewer CSO iterations for testing
        phi=0.1                # MATLAB phi = 0.1
    )

    print("Initialization successful")
    print(f"Parameters: dimension={iaf_fbo.dimension}, n_initial={iaf_fbo.n_initial}")
    print(f"CSO params: pop_size={iaf_fbo.pop_size}, max_iter={iaf_fbo.max_iter}, phi={iaf_fbo.phi}")

    # Test one iteration
    try:
        results = iaf_fbo.run(n_runs=1, verbose=True)
        print("Algorithm run successful")

        # Check results structure
        for client_id, result in results[0].items():
            print(f'Client {client_id}: best_y = {result["best_y"]:.6f}')

        return True

    except Exception as e:
        print(f'Error during execution: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_acquisition_functions():
    """Test acquisition function implementations against MATLAB formulas"""
    print("\nTesting acquisition functions...")

    from utils import acquisition_function
    import numpy as np

    # Test data (normalized as in MATLAB)
    pop_obj = np.array([0.1, 0.5, 0.9])  # Normalized predictions
    mse = np.array([0.01, 0.04, 0.09])   # Variance values
    a1_obj = np.array([0.0, 0.3, 0.6, 1.0])  # Normalized historical objectives

    print("Test data:")
    print(f"  pop_obj (predictions): {pop_obj}")
    print(f"  mse (variance): {mse}")
    print(f"  a1_obj (historical): {a1_obj}")

    # Test UCB (flag=1): PopObj + 2*sqrt(MSE)
    ucb_result = acquisition_function(pop_obj, mse, a1_obj, 1)
    ucb_expected = pop_obj + 2 * np.sqrt(mse)
    print(f"UCB result: {ucb_result}")
    print(f"UCB expected: {ucb_expected}")
    print(f"UCB match: {np.allclose(ucb_result, ucb_expected)}")

    # Test LCB (flag=2): PopObj - 2*sqrt(MSE)
    lcb_result = acquisition_function(pop_obj, mse, a1_obj, 2)
    lcb_expected = pop_obj - 2 * np.sqrt(mse)
    print(f"LCB result: {lcb_result}")
    print(f"LCB expected: {lcb_expected}")
    print(f"LCB match: {np.allclose(lcb_result, lcb_expected)}")

    # Test EI (flag=0)
    ei_result = acquisition_function(pop_obj, mse, a1_obj, 0)
    print(f"EI result: {ei_result}")

    return True

def test_cso_optimizer():
    """Test CSO optimizer with MATLAB-matching parameters"""
    print("\nTesting CSO Optimizer...")

    from cso_optimizer import CSO
    from neural_classifier import PairwiseClassifier
    import numpy as np

    # Create a simple test classifier
    classifier = PairwiseClassifier(input_dim=20)  # 2*10 dimensions

    # Test CSO parameters matching MATLAB
    cso = CSO(pop_size=20, max_iter=5, phi=0.1)  # Small values for testing
    bounds = (-5.0, 5.0)
    dimension = 10

    print(f"CSO parameters: pop_size={cso.pop_size}, max_iter={cso.max_iter}, phi={cso.phi}")

    # Test optimization
    try:
        best_solution = cso.optimize(classifier, bounds, dimension)
        print(f"CSO optimization successful")
        print(f"Best solution shape: {best_solution.shape}")
        print(f"Best solution in bounds: {np.all(best_solution >= bounds[0]) and np.all(best_solution <= bounds[1])}")
        return True
    except Exception as e:
        print(f"CSO optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gp_functionality():
    """Test Gaussian Process functionality"""
    print("\nTesting Gaussian Process...")
    
    from gaussian_process import DACELikeGP
    import numpy as np
    
    # Generate test data
    X = np.random.rand(20, 3)
    y = np.sum(X**2, axis=1) + 0.1 * np.random.randn(20)
    
    # Test GP
    gp = DACELikeGP(theta_init=np.ones(3))
    gp.fit(X, y)
    
    # Test prediction
    X_test = np.random.rand(5, 3)
    mean, var = gp.predict(X_test, return_std=True)
    
    print(f"GP prediction mean: {mean}")
    print(f"GP prediction variance: {var}")
    
    return True

def test_neural_classifier():
    """Test neural classifier functionality"""
    print("\nTesting Neural Classifier...")
    
    from neural_classifier import PairwiseClassifier, ClassifierTrainer, create_pairwise_dataset
    import numpy as np
    
    # Generate test data
    X = np.random.rand(20, 4)
    y = np.random.rand(20)
    
    # Create pairwise dataset
    X_pairs, y_pairs = create_pairwise_dataset(X, y)
    print(f"Pairwise dataset shape: {X_pairs.shape}, {y_pairs.shape}")
    
    # Test classifier
    classifier = PairwiseClassifier(input_dim=X_pairs.shape[1])
    trainer = ClassifierTrainer(epochs=10)
    
    # Train classifier
    trained_classifier = trainer.train(classifier, X_pairs, y_pairs)
    print("Classifier training successful")
    
    return True

def main():
    """Main test function"""
    print("=" * 60)
    print("IAF-FBO Algorithm Consistency Test")
    print("=" * 60)
    
    tests = [
        ("Acquisition Functions", test_acquisition_functions),
        ("CSO Optimizer", test_cso_optimizer),
        ("Gaussian Process", test_gp_functionality),
        ("Neural Classifier", test_neural_classifier),
        ("Basic Functionality", test_basic_functionality),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"Test {test_name} failed: {e}")
            import traceback
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:30} {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall: {'PASS' if all_passed else 'FAIL'}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
