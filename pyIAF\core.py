"""
Core IAF-FBO Algorithm Implementation
"""

import numpy as np
try:
    import torch
except ImportError:
    print("Warning: torch not available, some features may not work")
    torch = None

from sklearn.cluster import KMeans

# 尝试相对导入，失败则使用绝对导入
try:
    from .gaussian_process import DACELikeGP
    from .neural_classifier import PairwiseClassifier, ClassifierTrainer, create_pairwise_dataset
    from .neural_classifier import aggregate_classifiers, measure_classifier_similarity
    from .cso_optimizer import competitive_swarm_optimization
    from .utils import lhs_classic, acquisition_function, data_process, normalize_data
    from .test_functions import choose_problem
except ImportError:
    from gaussian_process import DACELikeGP
    from neural_classifier import PairwiseClassifier, ClassifierTrainer, create_pairwise_dataset
    from neural_classifier import aggregate_classifiers, measure_classifier_similarity
    from cso_optimizer import competitive_swarm_optimization
    from utils import lhs_classic, acquisition_function, data_process, normalize_data
    from test_functions import choose_problem


class IAF_FBO:
    """
    Implicit Acquisition Function - Federated Bayesian Optimization
    """
    
    def __init__(self, client_ids=None, dimension=10, n_initial=50, max_fe=60,
                 ucb_flag=2, n_clusters=6, privacy_noise=0.0, transfer_flag=True,
                 pop_size=100, max_iter=100, phi=0.1):
        """
        Initialize IAF-FBO algorithm
        
        Args:
            client_ids: List of client IDs (default: 1-18)
            dimension: Problem dimension (default: 10, matching MATLAB D=10)
            n_initial: Number of initial samples (default: 50, matching MATLAB N=50)
            max_fe: Maximum function evaluations per client (default: 60, matching MATLAB MAXFE=60)
            ucb_flag: Acquisition function type (0=EI, 1=UCB, 2=LCB, default: 2 matching MATLAB UCB_Flag=2)
            n_clusters: Number of clusters for client grouping (default: 6, matching MATLAB cl_num=6)
            privacy_noise: Privacy noise level (default: 0.0, matching MATLAB p=0)
            transfer_flag: Whether to enable knowledge transfer (default: True, matching MATLAB flag_transfer=1)
            pop_size: Population size for CSO (default: 100, matching MATLAB popsize=100)
            max_iter: Maximum iterations for CSO (default: 100, matching MATLAB wmax=100)
            phi: Learning factor for CSO (default: 0.1, matching MATLAB phi=0.1)
        """
        self.client_ids = client_ids if client_ids else list(range(1, 19))
        self.dimension = dimension
        self.n_initial = n_initial
        self.max_fe = max_fe
        self.ucb_flag = ucb_flag
        self.n_clusters = n_clusters
        self.privacy_noise = privacy_noise
        self.transfer_flag = transfer_flag
        self.pop_size = pop_size
        self.max_iter = max_iter
        self.phi = phi
        
        # Initialize client data
        self.client_data = {}
        self.client_functions = {}
        self.client_bounds = {}
        self.client_gps = {}
        self.client_classifiers = {}
        self.client_new_solutions = {}
        self.client_new_objectives = {}
        
        # Initialize problems for each client
        self._initialize_problems()
        
        # Initialize GP parameters
        self.theta = {}
        for client_id in self.client_ids:
            self.theta[client_id] = 5.0 * np.ones(self.dimension)
    
    def _initialize_problems(self):
        """Initialize optimization problems for each client"""
        for client_id in self.client_ids:
            func, bounds, rotation_matrix, optimal_point = choose_problem(
                client_id, self.dimension
            )
            
            self.client_functions[client_id] = func
            self.client_bounds[client_id] = bounds
            
            # Generate initial samples using LHS
            lower, upper = bounds
            lhs_samples = lhs_classic(self.n_initial, self.dimension)
            initial_x = lhs_samples * (upper - lower) + lower
            
            # Shuffle samples
            indices = np.random.permutation(self.n_initial)
            initial_x = initial_x[indices]
            
            # Evaluate initial samples
            initial_y = func(initial_x)
            
            self.client_data[client_id] = {
                'X': initial_x,
                'y': initial_y
            }
            
            # Initialize empty new solutions
            self.client_new_solutions[client_id] = []
            self.client_new_objectives[client_id] = []
    
    def run(self, n_runs=1, verbose=True):
        """
        Run the IAF-FBO algorithm
        
        Args:
            n_runs: Number of independent runs
            verbose: Whether to print progress
        
        Returns:
            Results dictionary
        """
        results = []
        
        for run in range(n_runs):
            if verbose:
                print(f"Starting run {run + 1}/{n_runs}")
            
            # Reset for new run
            self._initialize_problems()
            
            # Main optimization loop
            for round_num in range(self.max_fe):
                if verbose:
                    print(f"Round {round_num + 1}/{self.max_fe}")
                
                # Step 1: Train local GPs and classifiers
                self._train_local_models()
                
                # Step 2: Measure client similarity and cluster
                cluster_assignments = self._cluster_clients()
                
                # Step 3: Optimize global acquisition function for each client
                self._optimize_global_acquisition(cluster_assignments)
                
                # Print current best values
                if verbose:
                    for client_id in self.client_ids[:5]:  # Print first 5 clients
                        all_y = np.concatenate([
                            self.client_data[client_id]['y'],
                            self.client_new_objectives[client_id]
                        ]) if self.client_new_objectives[client_id] else self.client_data[client_id]['y']
                        
                        best_y = np.min(all_y)
                        print(f"Client {client_id} best: {best_y:.6f}")
            
            # Collect results for this run
            run_results = self._collect_results()
            results.append(run_results)
        
        return results
    
    def _train_local_models(self):
        """Train local Gaussian Processes and classifiers for each client"""
        n_test_samples = 100  # Number of test samples for AF evaluation
        
        for client_id in self.client_ids:
            # Get current data
            if self.client_new_solutions[client_id]:
                X_all = np.vstack([
                    self.client_data[client_id]['X'],
                    np.array(self.client_new_solutions[client_id])
                ])
                y_all = np.concatenate([
                    self.client_data[client_id]['y'],
                    np.array(self.client_new_objectives[client_id])
                ])
            else:
                X_all = self.client_data[client_id]['X']
                y_all = self.client_data[client_id]['y']
            
            # Normalize objectives (matching MATLAB: A1Obj_norm = (A1Obj - min(A1Obj) )./(max(A1Obj)-min(A1Obj)))
            y_min = np.min(y_all)
            y_max = np.max(y_all)
            if y_max > y_min:
                y_all_norm = (y_all - y_min) / (y_max - y_min)
            else:
                y_all_norm = np.zeros_like(y_all)

            # Remove duplicate points (matching MATLAB: [C,ia,ic] = unique(A1Dec,'rows','stable'))
            unique_indices = []
            seen = set()
            for i, x in enumerate(X_all):
                x_tuple = tuple(x)
                if x_tuple not in seen:
                    seen.add(x_tuple)
                    unique_indices.append(i)

            X_unique = X_all[unique_indices]
            y_unique_norm = y_all_norm[unique_indices]
            y_unique = y_all[unique_indices]

            # Train GP on normalized objectives
            gp = DACELikeGP(
                theta_init=self.theta[client_id],
                theta_bounds=[(1e-5, 100)] * self.dimension
            )
            gp.fit(X_unique, y_unique_norm)
            self.client_gps[client_id] = gp
            self.theta[client_id] = gp.theta
            
            # Generate test samples for AF evaluation (matching MATLAB: PopDec = PopDecTest.*(bu-bd)+ones(N_notR_ini,1).*bd)
            lower, upper = self.client_bounds[client_id]
            test_samples = np.random.uniform(lower, upper, (n_test_samples, self.dimension))

            # Predict with GP (matching MATLAB: [PopObj(i,:),~,MSE(i,:)] = predictor(PopDec(i,:),Model{ff}))
            pred_mean, pred_var = gp.predict(test_samples, return_std=True)

            # Calculate acquisition function values using normalized objectives
            # (matching MATLAB: objs = AF(PopObj,MSE,A1Obj_norm,UCB_Flag))
            af_values = acquisition_function(pred_mean, pred_var, y_unique_norm, self.ucb_flag)
            
            # Create pairwise dataset
            # For training data, use the unique data and calculate AF values for them
            train_af_values = acquisition_function(
                y_unique_norm, np.zeros_like(y_unique_norm), y_unique_norm, self.ucb_flag
            )

            combined_X = np.vstack([X_unique, test_samples])
            combined_af = np.concatenate([train_af_values, af_values])
            
            # Add privacy noise if specified
            if self.privacy_noise > 0:
                noise = np.random.normal(0, self.privacy_noise, len(combined_af))
                combined_af += noise
            
            X_pairs, y_pairs = create_pairwise_dataset(combined_X, combined_af)
            
            # Shuffle pairwise data
            indices = np.random.permutation(len(X_pairs))
            X_pairs = X_pairs[indices]
            y_pairs = y_pairs[indices]
            
            # Split into train/test
            X_train, y_train, X_test, y_test = data_process(X_pairs, y_pairs)
            
            # Normalize input data
            X_train_norm = normalize_data(X_train, self.client_bounds[client_id])
            
            # Train classifier
            input_dim = X_train.shape[1]
            classifier = PairwiseClassifier(input_dim)
            
            # Use transfer learning if enabled
            if (self.transfer_flag and 
                hasattr(self, 'aggregated_classifiers') and 
                len(self.client_new_solutions[client_id]) > 0):
                
                # Use aggregated classifier with 50% probability
                if np.random.rand() < 0.5 and client_id in self.aggregated_classifiers:
                    classifier = self.aggregated_classifiers[client_id]
            
            trainer = ClassifierTrainer(epochs=50)
            classifier = trainer.train(classifier, X_train_norm, y_train)
            
            self.client_classifiers[client_id] = classifier
    
    def _cluster_clients(self):
        """Cluster clients based on classifier similarity"""
        if len(self.client_classifiers) < self.n_clusters:
            # If fewer clients than clusters, each client is its own cluster
            return {i: [client_id] for i, client_id in enumerate(self.client_ids)}
        
        classifiers_list = [self.client_classifiers[cid] for cid in self.client_ids]
        cluster_assignments = measure_classifier_similarity(
            classifiers_list, self.n_clusters
        )
        
        # Group clients by cluster
        clusters = {}
        for i, client_id in enumerate(self.client_ids):
            cluster_id = cluster_assignments[i]
            if cluster_id not in clusters:
                clusters[cluster_id] = []
            clusters[cluster_id].append(client_id)
        
        return clusters
    
    def _optimize_global_acquisition(self, clusters):
        """Optimize global acquisition function using aggregated classifiers"""
        self.aggregated_classifiers = {}
        
        for cluster_id, client_ids_in_cluster in clusters.items():
            # Aggregate classifiers in this cluster
            classifiers_to_aggregate = [
                self.client_classifiers[cid] for cid in client_ids_in_cluster
            ]
            
            if len(classifiers_to_aggregate) > 1:
                aggregated_classifier = aggregate_classifiers(
                    {i: clf for i, clf in enumerate(classifiers_to_aggregate)},
                    list(range(len(classifiers_to_aggregate)))
                )
            else:
                aggregated_classifier = classifiers_to_aggregate[0]
            
            # Optimize for each client in the cluster
            for client_id in client_ids_in_cluster:
                self.aggregated_classifiers[client_id] = aggregated_classifier
                
                # Prepare initial population for CSO
                initial_pop = None
                if self.client_new_solutions[client_id]:
                    initial_pop = np.array(self.client_new_solutions[client_id])
                
                # Run CSO optimization
                bounds = self.client_bounds[client_id]
                best_solution = competitive_swarm_optimization(
                    aggregated_classifier,
                    bounds,
                    self.dimension,
                    initial_population=initial_pop,
                    pop_size=self.pop_size,
                    max_iter=self.max_iter,
                    phi=self.phi
                )
                
                # Evaluate new solution
                func = self.client_functions[client_id]
                new_objective = func(best_solution.reshape(1, -1))[0]
                
                # Store new solution
                self.client_new_solutions[client_id].append(best_solution)
                self.client_new_objectives[client_id].append(new_objective)
    
    def _collect_results(self):
        """Collect optimization results"""
        results = {}
        
        for client_id in self.client_ids:
            # Combine all data
            if self.client_new_solutions[client_id]:
                all_X = np.vstack([
                    self.client_data[client_id]['X'],
                    np.array(self.client_new_solutions[client_id])
                ])
                all_y = np.concatenate([
                    self.client_data[client_id]['y'],
                    np.array(self.client_new_objectives[client_id])
                ])
            else:
                all_X = self.client_data[client_id]['X']
                all_y = self.client_data[client_id]['y']
            
            results[client_id] = {
                'X': all_X,
                'y': all_y,
                'best_y': np.min(all_y),
                'best_X': all_X[np.argmin(all_y)]
            }
        
        return results
