#!/usr/bin/env python3
"""
简化版MATLAB对齐实验
18个客户端，10维，110次真实评估（50初始+60优化），运行1轮
"""

import sys
import os
import numpy as np
import time
from datetime import datetime

# 切换到pyIAF目录并导入
os.chdir('pyIAF')
sys.path.insert(0, '.')

def run_matlab_aligned_experiment():
    """运行与MATLAB完全对齐的实验"""
    print("=" * 80)
    print("IAF-FBO Python版 - MATLAB参数对齐实验")
    print("=" * 80)
    
    # MATLAB对齐参数
    print("实验参数配置（与MATLAB对齐）:")
    
    # 对应MATLAB classifier_BO_multitask_18client.m 的参数
    client_ids = list(range(1, 19))  # IFU_ = [1:18] - 18个客户端
    dimension = 10                   # D = 10
    n_initial = 50                   # N = 50
    max_fe = 60                      # MAXFE = 60
    ucb_flag = 2                     # UCB_Flag = 2 (LCB)
    n_clusters = 6                   # cl_num = 6
    privacy_noise = 0.0              # p = 0
    transfer_flag = True             # flag_transfer = 1
    pop_size = 100                   # popsize = 100
    max_iter = 100                   # wmax = 100
    phi = 0.1                        # phi = 0.1
    
    total_evaluations = n_initial + max_fe  # 50 + 60 = 110
    
    print(f"  客户端数量: {len(client_ids)}")
    print(f"  问题维度: {dimension}")
    print(f"  初始样本数: {n_initial}")
    print(f"  优化迭代数: {max_fe}")
    print(f"  总评估次数: {total_evaluations}")
    print(f"  获取函数: {'LCB' if ucb_flag == 2 else 'UCB' if ucb_flag == 1 else 'EI'}")
    print(f"  聚类数量: {n_clusters}")
    print(f"  迁移学习: {'启用' if transfer_flag else '禁用'}")
    print(f"  隐私噪声: {privacy_noise}")
    print(f"  CSO种群大小: {pop_size}")
    print(f"  CSO最大迭代: {max_iter}")
    print(f"  CSO学习因子: {phi}")
    print()
    
    try:
        # 导入核心模块
        from core import IAF_FBO
        
        print("开始实验...")
        start_time = time.time()
        
        # 创建IAF-FBO实例
        iaf_fbo = IAF_FBO(
            client_ids=client_ids,
            dimension=dimension,
            n_initial=n_initial,
            max_fe=max_fe,
            ucb_flag=ucb_flag,
            n_clusters=n_clusters,
            privacy_noise=privacy_noise,
            transfer_flag=transfer_flag,
            pop_size=pop_size,
            max_iter=max_iter,
            phi=phi
        )
        
        print("IAF-FBO实例创建成功")
        print("开始优化过程...")
        
        # 运行优化 (1轮)
        results = iaf_fbo.run(n_runs=1, verbose=True)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print("\n" + "=" * 80)
        print("实验结果")
        print("=" * 80)
        
        # 分析结果
        run_result = results[0]
        
        print(f"运行时间: {elapsed_time:.2f} 秒")
        print(f"成功完成 {len(run_result)} 个客户端的优化")
        print()
        
        # 详细结果
        print("各客户端最优结果:")
        print(f"{'客户端':<8} {'最优值':<15} {'评估次数':<10}")
        print("-" * 40)
        
        all_best_values = []
        for client_id in sorted(run_result.keys()):
            result = run_result[client_id]
            best_y = result['best_y']
            n_evals = len(result['y'])
            
            all_best_values.append(best_y)
            print(f"客户端{client_id:<3} {best_y:<15.6f} {n_evals:<10}")
        
        print("-" * 40)
        print(f"平均最优值: {np.mean(all_best_values):.6f}")
        print(f"最佳最优值: {np.min(all_best_values):.6f}")
        print(f"最差最优值: {np.max(all_best_values):.6f}")
        print(f"标准差: {np.std(all_best_values):.6f}")
        
        # 验证评估次数
        expected_evals = n_initial + max_fe
        actual_evals = [len(run_result[cid]['y']) for cid in client_ids]
        all_correct = all(n == expected_evals for n in actual_evals)
        
        print(f"\n评估次数验证:")
        print(f"  期望评估次数: {expected_evals}")
        print(f"  实际评估次数: {actual_evals[0]} (所有客户端)")
        print(f"  验证结果: {'✓ 正确' if all_correct else '✗ 错误'}")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"IAF_FBO_MATLAB_Aligned_{len(client_ids)}clients_D{dimension}_FE{total_evaluations}_{timestamp}.npz"
        
        # 准备保存数据
        save_data = {
            'all_best_values': all_best_values,
            'mean_best': np.mean(all_best_values),
            'min_best': np.min(all_best_values),
            'max_best': np.max(all_best_values),
            'std_best': np.std(all_best_values),
            'elapsed_time': elapsed_time,
            'parameters': {
                'client_ids': client_ids,
                'dimension': dimension,
                'n_initial': n_initial,
                'max_fe': max_fe,
                'total_evaluations': total_evaluations,
                'ucb_flag': ucb_flag,
                'n_clusters': n_clusters,
                'transfer_flag': transfer_flag,
                'privacy_noise': privacy_noise,
                'pop_size': pop_size,
                'max_iter': max_iter,
                'phi': phi
            }
        }
        
        # 添加每个客户端的详细结果
        for client_id, result in run_result.items():
            save_data[f'client_{client_id}_best_y'] = result['best_y']
            save_data[f'client_{client_id}_best_X'] = result['best_X']
            save_data[f'client_{client_id}_all_y'] = result['y']
        
        np.savez(filename, **save_data)
        print(f"\n结果已保存到: {filename}")
        
        print("\n" + "=" * 80)
        print("✅ 实验完成！")
        print("✓ 参数与MATLAB版本完全对齐")
        print("✓ 18个客户端，10维问题")
        print("✓ 110次真实函数评估 (50初始+60优化)")
        print("✓ 运行1轮完成")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 实验执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("启动MATLAB对齐实验...")
    
    # 设置随机种子
    np.random.seed(42)
    
    success = run_matlab_aligned_experiment()
    
    if success:
        print("\n🎉 实验成功完成！")
        print("Python版本已与MATLAB版本参数完全对齐。")
        print("结果文件已保存，可用于与MATLAB结果对比。")
    else:
        print("\n💥 实验失败，请检查错误信息。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
