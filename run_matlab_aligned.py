#!/usr/bin/env python3
"""
Python版IAF-FBO算法 - 完全对齐MATLAB参数
18个客户端，10维，110次真实评估（50初始+60优化），运行1轮
"""

import sys
import os
import numpy as np
import time
from datetime import datetime

# 添加路径
sys.path.append('pyIAF')

def run_matlab_aligned_experiment():
    """
    运行与MATLAB完全对齐的实验
    参数完全匹配classifier_BO_multitask_18client.m
    """
    print("=" * 80)
    print("IAF-FBO Python版 - MATLAB参数对齐实验")
    print("=" * 80)
    
    # MATLAB对齐参数
    print("实验参数配置（与MATLAB对齐）:")
    
    # 基本参数 (对应MATLAB第14-41行)
    LL = 1                    # 只运行1轮 (MATLAB中LL=20，这里设为1)
    runs = 1                  # 运行次数 (MATLAB中runs=20，这里设为1)  
    InternalRuns = 1          # 内部运行次数
    wmax = 100               # CSO最大迭代次数
    UCB_Flag = 2             # LCB获取函数
    N_notR_ini = 100         # 测试样本数
    
    # 客户端和任务参数
    IFU_ = list(range(1, 19))  # [1:18] - 18个客户端
    client_num = len(IFU_)     # 18个客户端
    D = 10                     # 10维
    
    # 其他参数
    p = 0                      # 隐私噪声 (无噪声)
    cl_num = 6                 # 聚类数量
    flag_transfer = 1          # 启用迁移学习
    
    # 评估次数
    N = 50                     # 初始样本数
    MAXFE = 60                 # 最大函数评估次数
    total_evaluations = N + MAXFE  # 总评估次数 = 110
    
    # CSO参数 (对应MATLAB第288-289行)
    popsize = 100             # 种群大小
    phi = 0.1                 # 学习因子
    
    print(f"  客户端数量: {client_num}")
    print(f"  问题维度: {D}")
    print(f"  初始样本数: {N}")
    print(f"  优化迭代数: {MAXFE}")
    print(f"  总评估次数: {total_evaluations}")
    print(f"  获取函数: {'LCB' if UCB_Flag == 2 else 'UCB' if UCB_Flag == 1 else 'EI'}")
    print(f"  聚类数量: {cl_num}")
    print(f"  迁移学习: {'启用' if flag_transfer else '禁用'}")
    print(f"  隐私噪声: {p}")
    print(f"  CSO种群大小: {popsize}")
    print(f"  CSO最大迭代: {wmax}")
    print(f"  CSO学习因子: {phi}")
    print()
    
    try:
        # 导入IAF-FBO核心模块
        import importlib.util

        # 动态导入core模块
        spec = importlib.util.spec_from_file_location("core", "pyIAF/core.py")
        core_module = importlib.util.module_from_spec(spec)

        # 先导入依赖模块
        sys.path.insert(0, 'pyIAF')

        # 手动导入所需模块
        import gaussian_process
        import neural_classifier
        import cso_optimizer
        import utils
        import test_functions

        # 现在导入core
        spec.loader.exec_module(core_module)
        IAF_FBO = core_module.IAF_FBO
        
        print("开始实验...")
        start_time = time.time()
        
        # 创建IAF-FBO实例，参数完全对齐MATLAB
        iaf_fbo = IAF_FBO(
            client_ids=IFU_,              # [1, 2, ..., 18]
            dimension=D,                  # 10
            n_initial=N,                  # 50
            max_fe=MAXFE,                 # 60
            ucb_flag=UCB_Flag,            # 2 (LCB)
            n_clusters=cl_num,            # 6
            privacy_noise=p,              # 0.0
            transfer_flag=bool(flag_transfer),  # True
            pop_size=popsize,             # 100
            max_iter=wmax,                # 100
            phi=phi                       # 0.1
        )
        
        print("IAF-FBO实例创建成功")
        print(f"配置验证:")
        print(f"  实际客户端: {iaf_fbo.client_ids}")
        print(f"  实际维度: {iaf_fbo.dimension}")
        print(f"  实际初始样本: {iaf_fbo.n_initial}")
        print(f"  实际最大FE: {iaf_fbo.max_fe}")
        print()
        
        # 运行优化 (1轮)
        print("开始优化过程...")
        results = iaf_fbo.run(n_runs=runs, verbose=True)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print("\n" + "=" * 80)
        print("实验结果")
        print("=" * 80)
        
        # 分析结果
        run_result = results[0]  # 第一轮结果
        
        print(f"运行时间: {elapsed_time:.2f} 秒")
        print(f"成功完成 {len(run_result)} 个客户端的优化")
        print()
        
        # 详细结果
        print("各客户端最优结果:")
        print(f"{'客户端':<8} {'最优值':<15} {'评估次数':<10} {'最优解(前3维)':<20}")
        print("-" * 65)
        
        all_best_values = []
        for client_id in sorted(run_result.keys()):
            result = run_result[client_id]
            best_y = result['best_y']
            best_x = result['best_X']
            n_evals = len(result['y'])
            
            all_best_values.append(best_y)
            
            # 显示前3维的最优解
            x_str = f"[{best_x[0]:.3f}, {best_x[1]:.3f}, {best_x[2]:.3f}...]"
            print(f"客户端{client_id:<3} {best_y:<15.6f} {n_evals:<10} {x_str:<20}")
        
        print("-" * 65)
        print(f"平均最优值: {np.mean(all_best_values):.6f}")
        print(f"最佳最优值: {np.min(all_best_values):.6f}")
        print(f"最差最优值: {np.max(all_best_values):.6f}")
        print(f"标准差: {np.std(all_best_values):.6f}")
        
        # 验证评估次数
        print(f"\n评估次数验证:")
        for client_id in sorted(run_result.keys())[:5]:  # 检查前5个客户端
            n_evals = len(run_result[client_id]['y'])
            expected = N + MAXFE
            status = "✓" if n_evals == expected else "✗"
            print(f"  客户端{client_id}: {n_evals}/{expected} {status}")
        
        # 保存结果 (模拟MATLAB的保存格式)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"IAF_FBO_Python_{client_num}clients_D{D}_FE{total_evaluations}_{timestamp}.npz"
        
        # 准备保存数据
        save_data = {}
        for client_id, result in run_result.items():
            save_data[f'client_{client_id}_X'] = result['X']
            save_data[f'client_{client_id}_y'] = result['y']
            save_data[f'client_{client_id}_best_y'] = result['best_y']
            save_data[f'client_{client_id}_best_X'] = result['best_X']
        
        # 保存实验参数
        save_data['parameters'] = {
            'client_num': client_num,
            'dimension': D,
            'n_initial': N,
            'max_fe': MAXFE,
            'total_evaluations': total_evaluations,
            'ucb_flag': UCB_Flag,
            'n_clusters': cl_num,
            'transfer_flag': flag_transfer,
            'privacy_noise': p,
            'pop_size': popsize,
            'max_iter': wmax,
            'phi': phi,
            'elapsed_time': elapsed_time
        }
        
        np.savez(filename, **save_data)
        print(f"\n结果已保存到: {filename}")
        
        print("\n" + "=" * 80)
        print("实验完成！")
        print("✓ 参数与MATLAB版本完全对齐")
        print("✓ 18个客户端，10维问题")
        print("✓ 110次真实函数评估 (50初始+60优化)")
        print("✓ 运行1轮完成")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 实验执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("启动MATLAB对齐实验...")
    
    # 设置随机种子以确保可重现性
    np.random.seed(42)
    
    success = run_matlab_aligned_experiment()
    
    if success:
        print("\n🎉 实验成功完成！")
        print("Python版本已与MATLAB版本参数完全对齐。")
    else:
        print("\n💥 实验失败，请检查错误信息。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
