"""
Main script for IAF-FBO algorithm - Python version of classifier_BO_multitask_18client.m
"""

import numpy as np
import time
import pickle
import os
from datetime import datetime
from core import IAF_FBO


def main_experiment():
    """
    Main experiment function replicating the MATLAB script logic
    """
    print("IAF-FBO: Federated Bayesian Many-Task Optimization")
    print("Python implementation")
    print("=" * 60)
    
    # Set random seed for reproducibility
    np.random.seed(int(time.time()) % (2**32 - 1))
    
    # Experiment parameters (matching MATLAB script exactly)
    n_runs = 20  # Number of independent runs
    client_ids = list(range(1, 19))  # 18 clients (tasks)
    dimension = 10  # MATLAB uses D = 10, not 50
    n_initial = 50  # N = 50 in MATLAB
    max_fe = 60  # MAXFE = 60 in MATLAB
    ucb_flag = 2  # LCB acquisition function (UCB_Flag = 2)
    n_clusters = 6  # cl_num = 6 in MATLAB
    privacy_noise = 0.0  # p = 0 (no privacy noise in base experiment)
    transfer_flag = True  # flag_transfer = 1
    
    # CSO parameters (matching MATLAB script)
    pop_size = 100  # popsize = 100 in MATLAB
    max_iter = 100  # wmax = 100 in MATLAB
    phi = 0.1  # phi = 0.1 in MATLAB
    
    print(f"Configuration:")
    print(f"  Clients: {len(client_ids)}")
    print(f"  Dimension: {dimension}")
    print(f"  Initial samples: {n_initial}")
    print(f"  Max function evaluations: {max_fe}")
    print(f"  Acquisition function: {'LCB' if ucb_flag == 2 else 'UCB' if ucb_flag == 1 else 'EI'}")
    print(f"  Number of clusters: {n_clusters}")
    print(f"  Transfer learning: {transfer_flag}")
    print(f"  Privacy noise: {privacy_noise}")
    print(f"  Number of runs: {n_runs}")
    print()
    
    # Store results for all runs
    all_results = []
    
    # Run experiments
    for run in range(n_runs):
        print(f"Starting Run {run + 1}/{n_runs}")
        print("-" * 40)
        
        start_time = time.time()
        
        # Initialize IAF-FBO algorithm
        iaf_fbo = IAF_FBO(
            client_ids=client_ids,
            dimension=dimension,
            n_initial=n_initial,
            max_fe=max_fe,
            ucb_flag=ucb_flag,
            n_clusters=n_clusters,
            privacy_noise=privacy_noise,
            transfer_flag=transfer_flag,
            pop_size=pop_size,
            max_iter=max_iter,
            phi=phi
        )
        
        # Run optimization
        results = iaf_fbo.run(n_runs=1, verbose=True)
        
        end_time = time.time()
        run_time = end_time - start_time
        
        # Store results
        run_result = {
            'run_id': run + 1,
            'results': results[0],
            'runtime': run_time,
            'parameters': {
                'dimension': dimension,
                'n_initial': n_initial,
                'max_fe': max_fe,
                'ucb_flag': ucb_flag,
                'n_clusters': n_clusters,
                'privacy_noise': privacy_noise,
                'transfer_flag': transfer_flag
            }
        }
        
        all_results.append(run_result)
        
        # Print summary for this run
        print(f"\nRun {run + 1} Summary:")
        print(f"  Runtime: {run_time:.2f} seconds")
        
        best_objectives = []
        for client_id in client_ids:
            best_obj = results[0][client_id]['best_y']
            best_objectives.append(best_obj)
            if client_id <= 5:  # Print first 5 clients
                print(f"  Client {client_id}: {best_obj:.6f}")
        
        avg_best = np.mean(best_objectives)
        print(f"  Average best objective: {avg_best:.6f}")
        print()
    
    # Save results
    save_results(all_results, dimension, max_fe, ucb_flag, n_clusters, transfer_flag)
    
    # Print final statistics
    print_final_statistics(all_results)
    
    return all_results


def save_results(all_results, dimension, max_fe, ucb_flag, n_clusters, transfer_flag):
    """Save results to file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create filename similar to MATLAB version
    filename = (f"IAF_FBO_Python_{len(all_results[0]['results'])}tasks_"
               f"AF{ucb_flag}_D{dimension}_MAXFE{max_fe + all_results[0]['parameters']['n_initial']}_"
               f"trs{int(transfer_flag)}_clu{n_clusters}_{timestamp}.pkl")
    
    # Save as pickle file
    with open(filename, 'wb') as f:
        pickle.dump(all_results, f)
    
    print(f"Results saved to: {filename}")
    
    # Also save as text summary
    txt_filename = filename.replace('.pkl', '_summary.txt')
    with open(txt_filename, 'w') as f:
        f.write("IAF-FBO Experiment Results Summary\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Parameters:\n")
        f.write(f"  Number of tasks: {len(all_results[0]['results'])}\n")
        f.write(f"  Dimension: {dimension}\n")
        f.write(f"  Max FE: {max_fe}\n")
        f.write(f"  Acquisition function: {ucb_flag}\n")
        f.write(f"  Clusters: {n_clusters}\n")
        f.write(f"  Transfer: {transfer_flag}\n")
        f.write(f"  Number of runs: {len(all_results)}\n\n")
        
        # Statistics for each client
        for client_id in sorted(all_results[0]['results'].keys()):
            best_values = [run['results'][client_id]['best_y'] for run in all_results]
            f.write(f"Client {client_id}:\n")
            f.write(f"  Mean: {np.mean(best_values):.6f}\n")
            f.write(f"  Std:  {np.std(best_values):.6f}\n")
            f.write(f"  Min:  {np.min(best_values):.6f}\n")
            f.write(f"  Max:  {np.max(best_values):.6f}\n\n")


def print_final_statistics(all_results):
    """Print final statistics across all runs"""
    print("Final Statistics Across All Runs")
    print("=" * 50)
    
    n_runs = len(all_results)
    client_ids = sorted(all_results[0]['results'].keys())
    
    # Runtime statistics
    runtimes = [run['runtime'] for run in all_results]
    print(f"Runtime Statistics:")
    print(f"  Mean: {np.mean(runtimes):.2f} ± {np.std(runtimes):.2f} seconds")
    print(f"  Total: {np.sum(runtimes):.2f} seconds")
    print()
    
    # Objective statistics for each client
    print("Best Objective Statistics by Client:")
    print("Client ID | Mean ± Std | Min | Max")
    print("-" * 40)
    
    overall_means = []
    for client_id in client_ids:
        best_values = [run['results'][client_id]['best_y'] for run in all_results]
        mean_val = np.mean(best_values)
        std_val = np.std(best_values)
        min_val = np.min(best_values)
        max_val = np.max(best_values)
        
        overall_means.append(mean_val)
        print(f"{client_id:9d} | {mean_val:6.3f} ± {std_val:5.3f} | {min_val:6.3f} | {max_val:6.3f}")
    
    print("-" * 40)
    print(f"Overall   | {np.mean(overall_means):6.3f} ± {np.std(overall_means):5.3f}")
    print()


def run_ablation_study():
    """Run ablation study comparing different configurations"""
    print("Running Ablation Study")
    print("=" * 30)
    
    # Base configuration
    base_config = {
        'client_ids': list(range(1, 7)),  # Use 6 clients for faster testing
        'dimension': 20,
        'n_initial': 20,
        'max_fe': 20,
        'n_clusters': 3,
        'privacy_noise': 0.0,
        'pop_size': 50,
        'max_iter': 50
    }
    
    # Test different configurations
    configs = [
        {'name': 'LCB', 'ucb_flag': 2, 'transfer_flag': True},
        {'name': 'UCB', 'ucb_flag': 1, 'transfer_flag': True},
        {'name': 'EI', 'ucb_flag': 0, 'transfer_flag': True},
        {'name': 'No Transfer', 'ucb_flag': 2, 'transfer_flag': False},
    ]
    
    results_comparison = {}
    
    for config in configs:
        print(f"\nTesting configuration: {config['name']}")
        
        iaf_fbo = IAF_FBO(
            **base_config,
            ucb_flag=config['ucb_flag'],
            transfer_flag=config['transfer_flag']
        )
        
        results = iaf_fbo.run(n_runs=1, verbose=False)
        results_comparison[config['name']] = results[0]
        
        # Print summary
        best_objectives = [results[0][cid]['best_y'] for cid in base_config['client_ids']]
        print(f"  Average best objective: {np.mean(best_objectives):.6f}")
    
    return results_comparison


if __name__ == "__main__":
    # Choose experiment type
    experiment_type = input("Choose experiment type:\n"
                          "1. Main experiment (full)\n"
                          "2. Ablation study (quick)\n"
                          "3. Simple test\n"
                          "Enter choice (1-3): ").strip()
    
    if experiment_type == "1":
        results = main_experiment()
    elif experiment_type == "2":
        results = run_ablation_study()
    elif experiment_type == "3":
        # Simple test
        print("Running simple test...")
        iaf_fbo = IAF_FBO(
            client_ids=[1, 2, 3],
            dimension=10,
            n_initial=10,
            max_fe=5,
            ucb_flag=2,
            n_clusters=2,
            pop_size=20,
            max_iter=10
        )
        results = iaf_fbo.run(n_runs=1, verbose=True)
        print("Simple test completed!")
    else:
        print("Invalid choice. Running simple test...")
        iaf_fbo = IAF_FBO(
            client_ids=[1, 2],
            dimension=5,
            n_initial=5,
            max_fe=3,
            pop_size=10,
            max_iter=5
        )
        results = iaf_fbo.run(n_runs=1, verbose=True)
