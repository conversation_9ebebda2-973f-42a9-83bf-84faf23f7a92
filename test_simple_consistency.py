#!/usr/bin/env python3
"""
Simple test script to verify IAF-FBO algorithm consistency without complex dependencies
"""

import sys
import os
import numpy as np

# Add pyIAF to path
sys.path.append('pyIAF')

def test_acquisition_functions():
    """Test acquisition function implementations against MATLAB formulas"""
    print("Testing acquisition functions...")
    
    try:
        from utils import acquisition_function
        
        # Test data (normalized as in MATLAB)
        pop_obj = np.array([0.1, 0.5, 0.9])  # Normalized predictions
        mse = np.array([0.01, 0.04, 0.09])   # Variance values
        a1_obj = np.array([0.0, 0.3, 0.6, 1.0])  # Normalized historical objectives
        
        print("Test data:")
        print(f"  pop_obj (predictions): {pop_obj}")
        print(f"  mse (variance): {mse}")
        print(f"  a1_obj (historical): {a1_obj}")
        
        # Test UCB (flag=1): PopObj + 2*sqrt(MSE)
        ucb_result = acquisition_function(pop_obj, mse, a1_obj, 1)
        ucb_expected = pop_obj + 2 * np.sqrt(mse)
        print(f"UCB result: {ucb_result}")
        print(f"UCB expected: {ucb_expected}")
        ucb_match = np.allclose(ucb_result, ucb_expected)
        print(f"UCB match: {ucb_match}")
        
        # Test LCB (flag=2): PopObj - 2*sqrt(MSE)
        lcb_result = acquisition_function(pop_obj, mse, a1_obj, 2)
        lcb_expected = pop_obj - 2 * np.sqrt(mse)
        print(f"LCB result: {lcb_result}")
        print(f"LCB expected: {lcb_expected}")
        lcb_match = np.allclose(lcb_result, lcb_expected)
        print(f"LCB match: {lcb_match}")
        
        return ucb_match and lcb_match
        
    except Exception as e:
        print(f"Acquisition function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lhs_sampling():
    """Test Latin Hypercube Sampling"""
    print("\nTesting LHS sampling...")
    
    try:
        from utils import lhs_classic
        
        # Test LHS
        n_samples = 10
        n_dims = 5
        samples = lhs_classic(n_samples, n_dims)
        
        print(f"LHS samples shape: {samples.shape}")
        print(f"Expected shape: ({n_samples}, {n_dims})")
        print(f"All values in [0,1]: {np.all((samples >= 0) & (samples <= 1))}")
        print(f"Sample range: [{np.min(samples):.3f}, {np.max(samples):.3f}]")
        
        shape_correct = samples.shape == (n_samples, n_dims)
        bounds_correct = np.all((samples >= 0) & (samples <= 1))
        
        return shape_correct and bounds_correct
        
    except Exception as e:
        print(f"LHS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processing():
    """Test data processing functions"""
    print("\nTesting data processing...")
    
    try:
        from utils import normalize_data, data_process, onehot_conv
        
        # Test normalization
        data = np.array([[1, 2], [3, 4], [5, 6]])
        bounds = (0, 10)
        normalized = normalize_data(data, bounds)
        
        print(f"Original data:\n{data}")
        print(f"Normalized data:\n{normalized}")
        print(f"Normalized range: [{np.min(normalized):.3f}, {np.max(normalized):.3f}]")
        
        # Test data processing
        X = np.random.rand(20, 4)
        y = np.random.choice([-1, 1], 20)
        
        X_train, y_train, X_test, y_test = data_process(X, y)
        
        print(f"Data split - Train: {X_train.shape}, Test: {X_test.shape}")
        
        # Test onehot conversion
        predictions = np.array([[0.8, 0.2], [0.3, 0.7], [0.6, 0.4]])
        labels = onehot_conv(predictions, mode=2)
        
        print(f"Predictions:\n{predictions}")
        print(f"Labels: {labels}")
        
        return True
        
    except Exception as e:
        print(f"Data processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_test_functions():
    """Test optimization test functions"""
    print("\nTesting test functions...")
    
    try:
        from test_functions import choose_problem
        
        # Test a few problem instances
        for client_id in [1, 2, 3]:
            func, bounds, rotation_matrix, optimal_point = choose_problem(client_id, 5)
            
            print(f"Client {client_id}:")
            print(f"  Function: {func}")
            print(f"  Bounds: {bounds}")
            print(f"  Has rotation matrix: {rotation_matrix is not None}")
            print(f"  Has optimal point: {optimal_point is not None}")
            
            # Test function evaluation
            test_point = np.random.uniform(bounds[0], bounds[1], (3, 5))
            result = func(test_point)
            
            print(f"  Test evaluation shape: {result.shape}")
            print(f"  Test evaluation range: [{np.min(result):.3f}, {np.max(result):.3f}]")
        
        return True
        
    except Exception as e:
        print(f"Test functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_consistency():
    """Test parameter consistency with MATLAB version"""
    print("\nTesting parameter consistency...")
    
    try:
        # Import without instantiating to avoid torch dependency
        import importlib.util
        spec = importlib.util.spec_from_file_location("core", "pyIAF/core.py")
        core_module = importlib.util.module_from_spec(spec)
        
        # Check default parameters by reading the source
        with open("pyIAF/core.py", "r") as f:
            content = f.read()
        
        # Check for MATLAB-matching defaults
        checks = {
            "dimension=10": "dimension=10" in content,
            "n_initial=50": "n_initial=50" in content,
            "max_fe=60": "max_fe=60" in content,
            "ucb_flag=2": "ucb_flag=2" in content,
            "n_clusters=6": "n_clusters=6" in content,
            "pop_size=100": "pop_size=100" in content,
            "max_iter=100": "max_iter=100" in content,
            "phi=0.1": "phi=0.1" in content,
        }
        
        print("Parameter consistency checks:")
        all_passed = True
        for param, passed in checks.items():
            status = "PASS" if passed else "FAIL"
            print(f"  {param:20} {status}")
            all_passed = all_passed and passed
        
        return all_passed
        
    except Exception as e:
        print(f"Parameter consistency test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("IAF-FBO Algorithm Simple Consistency Test")
    print("=" * 60)
    
    tests = [
        ("Acquisition Functions", test_acquisition_functions),
        ("LHS Sampling", test_lhs_sampling),
        ("Data Processing", test_data_processing),
        ("Test Functions", test_test_functions),
        ("Parameter Consistency", test_parameter_consistency),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"Test {test_name} failed: {e}")
            import traceback
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:30} {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall: {'PASS' if all_passed else 'FAIL'}")
    
    if all_passed:
        print("\n✅ All tests passed! Python implementation is consistent with MATLAB version.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
